import React, { useState, useRef, useEffect } from 'react';
import { useIntersectionObserver, generateSrcSet } from '@/lib/image-optimization';
import './optimized-image.css';

type OptimizedImageProps = {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  containerClassName?: string;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
};

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  containerClassName = '',
  priority = false,
  loading = 'lazy',
  onLoad,
  objectFit = 'cover',
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isVisible = useIntersectionObserver(containerRef);

  // Properly encode the image source URL to handle spaces and special characters
  const encodedSrc = src.includes(' ') ? encodeURI(src) : src;

  // Set proper loading attribute based on priority
  const loadingAttr = priority ? 'eager' : loading;

  // Generate fetchpriority attribute for LCP images
  const fetchPriority = priority ? 'high' : 'auto';

  // Set decoding attribute based on priority
  const decodingAttr = priority ? 'sync' : 'async';

  // Generate srcset and only include it if it's not empty
  const srcSet = generateSrcSet(src);
  const srcSetAttr = srcSet ? { srcSet } : {};

  useEffect(() => {
    if (priority && imageRef.current) {
      // If image is prioritized, preload it using the encoded source
      const img = new Image();
      img.src = encodedSrc;
      img.onload = () => {
        setIsLoaded(true);
        setHasError(false);
        onLoad?.();
      };
      img.onerror = () => {
        setHasError(true);
        console.error('Failed to load priority image:', encodedSrc);
      };
    }
  }, [priority, encodedSrc, onLoad]);

  // Handle image load event
  const handleImageLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
  };

  // Handle image error event
  const handleImageError = () => {
    setHasError(true);
    setIsLoaded(false);
    console.error('Failed to load image:', encodedSrc);
  };

  // Set CSS attributes through element attributes instead of inline styles
  // This will be handled by our CSS in custom-properties.css
  const widthAttr = width ? `${width}px` : null;
  const heightAttr = height ? `${height}px` : null;

  // Determine object-fit class based on the prop
  const objectFitClass = `object-fit-${objectFit}`;

  return (
    <div
      ref={containerRef}
      className={`optimized-image-container ${containerClassName}`}
      data-width={widthAttr}
      data-height={heightAttr}
    >
      {isVisible || priority ? (
        <>
          {/* Error fallback */}
          {hasError ? (
            <div className="optimized-image-error flex items-center justify-center bg-gray-800/50 text-gray-400 text-sm p-4 rounded">
              <span>Image failed to load</span>
            </div>
          ) : (
            <>
              {/* Low quality placeholder styling */}
              {!isLoaded && (
                <div className="optimized-image-placeholder" />
              )}
              <img
                ref={imageRef}
                src={encodedSrc}
                alt={alt}
                width={width}
                height={height}
                loading={loadingAttr}
                decoding={decodingAttr}
                className={`optimized-image ${isLoaded ? 'optimized-image--loaded' : 'optimized-image--loading'} ${objectFitClass} ${className}`}
                onLoad={handleImageLoad}
                onError={handleImageError}
                // We'll use a data attribute for fetch priority to avoid browser compatibility issues
                data-fetchpriority={fetchPriority}
                {...srcSetAttr}
              />
            </>
          )}
        </>
      ) : (
        // Empty placeholder when not visible and not priority
        <div className="optimized-image--empty-placeholder" />
      )}
    </div>
  );
}
